#!/usr/bin/env python3

from geocode_addresses import is_in_bbox, BOCHNIA_BBOX

# Wsp<PERSON>łrzędne z logów, które były odrzucane
test_coordinates = [
    # <PERSON><PERSON><PERSON><PERSON>
    (50.0311111, 20.4641667, "Gawł<PERSON>"),
    # <PERSON><PERSON><PERSON><PERSON>
    (50.0038259, 20.4254081, "<PERSON><PERSON><PERSON><PERSON>"),
    # <PERSON><PERSON><PERSON><PERSON>
    (49.9247, 20.33297, "<PERSON><PERSON><PERSON><PERSON>"),
    # <PERSON><PERSON><PERSON><PERSON>
    (49.8041261, 20.417899, "<PERSON><PERSON><PERSON><PERSON>"),
    # <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
    (50.0375472, 20.5555532, "Dąbrów<PERSON>"),
    # <PERSON><PERSON><PERSON><PERSON><PERSON>
    (49.9661, 20.34681, "<PERSON>sz<PERSON><PERSON><PERSON>"),
    # <PERSON><PERSON><PERSON>
    (50.1221698, 20.4960167, "<PERSON><PERSON><PERSON>"),
    # <PERSON><PERSON> Wiś<PERSON>z
    (49.9253452, 20.486314, "<PERSON><PERSON> Wiśnicz"),
    # <PERSON>g<PERSON><PERSON><PERSON><PERSON>
    (49.9220748, 20.4014864, "<PERSON>g<PERSON><PERSON><PERSON><PERSON>"),
    # <PERSON><PERSON><PERSON>
    (49.91037, 20.3229594, "<PERSON><PERSON><PERSON>"),
    # <PERSON><PERSON>
    (50.113358, 20.3774597, "<PERSON><PERSON>"),
    # <PERSON><PERSON><PERSON><PERSON><PERSON>
    (49.9511392, 20.3198481, "<PERSON><PERSON><PERSON><PERSON><PERSON>"),
    # <PERSON><PERSON><PERSON>a
    (49.8452724, 20.3731933, "<PERSON><PERSON><PERSON><PERSON>"),
    # <PERSON><PERSON><PERSON><PERSON>
    (50.0321772, 20.4411838, "<PERSON><PERSON><PERSON>ów"),
    # <PERSON><PERSON><PERSON>
    (49.858264, 20.4049323, "<PERSON><PERSON><PERSON><PERSON>")
]

print(f"<PERSON><PERSON>ual<PERSON> bounding box: {<PERSON><PERSON>HNIA_BBOX}")
print("Test współrzędnych z logów geokodowania:")
print("=" * 60)

in_bbox_count = 0
out_bbox_count = 0

for lat, lon, name in test_coordinates:
    is_in = is_in_bbox(lat, lon, BOCHNIA_BBOX)
    status = "✓ W bbox" if is_in else "✗ Poza bbox"
    print(f"{status}: {name} ({lat}, {lon})")
    
    if is_in:
        in_bbox_count += 1
    else:
        out_bbox_count += 1

print("=" * 60)
print(f"W bounding box: {in_bbox_count}")
print(f"Poza bounding box: {out_bbox_count}")

if out_bbox_count > 0:
    print("\nSugerowany rozszerzony bounding box:")
    lats = [coord[0] for coord in test_coordinates]
    lons = [coord[1] for coord in test_coordinates]
    
    min_lat = min(lats) - 0.01  # margines
    max_lat = max(lats) + 0.01
    min_lon = min(lons) - 0.01
    max_lon = max(lons) + 0.01
    
    print(f"BOCHNIA_BBOX = [({min_lat:.3f}, {min_lon:.3f}), ({max_lat:.3f}, {max_lon:.3f})]")
