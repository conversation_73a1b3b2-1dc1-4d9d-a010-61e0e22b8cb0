#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test sprawdzający nową logikę sprawdzania kodów pocztowych
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from geocode_addresses import (
    extract_postal_code, 
    is_valid_postal_code, 
    parse_csv_address,
    is_valid_result,
    BOCHNIA_POSTAL_CODES
)

def test_postal_code_extraction():
    """Test wyodrębniania kodów pocztowych"""
    print("=== Test wyodrębniania kodów pocztowych ===")
    
    test_cases = [
        ("32-700 Bochnia,ul. Krakowska 15", "32-700"),
        ("32-722 Królówka,Królówka 263", "32-722"),
        ("32-813 Niedary,Niedary 44", "32-813"),
        ("Brak kodu pocztowego", None),
        ("", None),
        (None, None)
    ]
    
    for address, expected in test_cases:
        result = extract_postal_code(address)
        status = "✓" if result == expected else "✗"
        print(f"{status} '{address}' -> '{result}' (oczekiwano: '{expected}')")

def test_postal_code_validation():
    """Test sprawdzania poprawności kodów pocztowych"""
    print("\n=== Test sprawdzania poprawności kodów pocztowych ===")
    
    valid_codes = ["32-700", "32-722", "32-813", "32-015"]
    invalid_codes = ["30-001", "31-000", "33-100", None, ""]
    
    print("Kody prawidłowe:")
    for code in valid_codes:
        result = is_valid_postal_code(code)
        status = "✓" if result else "✗"
        print(f"{status} {code} -> {result}")
    
    print("\nKody nieprawidłowe:")
    for code in invalid_codes:
        result = is_valid_postal_code(code)
        status = "✓" if not result else "✗"
        print(f"{status} {code} -> {result}")

def test_address_parsing():
    """Test parsowania adresów z pliku CSV"""
    print("\n=== Test parsowania adresów ===")
    
    test_addresses = [
        "32-722 Królówka,Królówka 263",
        "32-700 Bochnia,ul. Krakowska 15",
        "32-813 Niedary,Niedary 44",
        "32-733 Leszczyna,działka nr 523 0"
    ]
    
    for address in test_addresses:
        result = parse_csv_address(address)
        if result:
            print(f"✓ '{address}'")
            print(f"  Kod: {result['postal_code']}")
            print(f"  Miasto: {result['city']}")
            print(f"  Ulica: {result['street_and_number']}")
        else:
            print(f"✗ Nie udało się sparsować: '{address}'")
        print()

def test_result_validation():
    """Test sprawdzania poprawności wyników geokodowania"""
    print("\n=== Test sprawdzania poprawności wyników ===")
    
    # Współrzędne w powiecie bocheńskim
    lat, lon = 49.967, 20.433  # Bochnia
    
    test_cases = [
        # Prawidłowy adres z kodem pocztowym powiatu bocheńskiego
        ("32-700 Bochnia,ul. Krakowska 15, małopolskie", True),
        # Nieprawidłowy kod pocztowy
        ("30-001 Kraków,ul. Floriańska 15, małopolskie", False),
        # Brak województwa
        ("32-700 Bochnia,ul. Krakowska 15", False),
        # Nieprawidłowe województwo
        ("32-700 Bochnia,ul. Krakowska 15, śląskie", False),
    ]
    
    for address_text, expected in test_cases:
        result = is_valid_result(lat, lon, address_text)
        status = "✓" if result == expected else "✗"
        print(f"{status} '{address_text}' -> {result} (oczekiwano: {expected})")

def main():
    """Uruchom wszystkie testy"""
    print("Testowanie nowej logiki sprawdzania kodów pocztowych dla powiatu bocheńskiego")
    print(f"Liczba kodów pocztowych w powiecie: {len(BOCHNIA_POSTAL_CODES)}")
    print(f"Kody: {sorted(BOCHNIA_POSTAL_CODES)}")
    print()
    
    test_postal_code_extraction()
    test_postal_code_validation()
    test_address_parsing()
    test_result_validation()
    
    print("\n=== Testy zakończone ===")

if __name__ == "__main__":
    main()
