import pandas as pd
import re

# Wczytaj plik CSV
df = pd.read_csv("powiat bocheński - adresy_geocode.csv", sep=',')

# Wyodrębnij kody pocztowe z adresów
postal_codes = set()
for address in df['adress']:
    # Szukaj wzorca XX-XXX na początku adresu
    match = re.match(r'^(\d{2}-\d{3})', address)
    if match:
        postal_codes.add(match.group(1))

# Posortuj i wyświetl
sorted_codes = sorted(postal_codes)
print("Kody pocztowe w powiecie bocheńskim:")
for code in sorted_codes:
    print(code)

print(f"\nLiczba unikalnych kodów pocztowych: {len(sorted_codes)}")
