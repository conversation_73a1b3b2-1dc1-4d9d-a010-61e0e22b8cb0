import pandas as pd
from geopy.geocoders import Nominatim
from geopy.exc import GeocoderTimedOut, GeocoderServiceError
import time
import ssl
import certifi
import logging
from datetime import datetime
import sys
import os
from tqdm import tqdm

# Bounding box dla powiatu bocheńskiego
# Format dla geopy: [(lat1, lon1), (lat2, lon2)] - południowy-zachód, północny-wschód
# <PERSON><PERSON><PERSON> o<PERSON> powiat bocheński w województwie małopolskim
# Rozszerzony na podstawie rzeczywistych współrzędnych miejscowości z logów geokodowania
# Obejmuje wszystkie miejscowości: <PERSON><PERSON><PERSON><PERSON> (49.85), <PERSON><PERSON><PERSON> (50.12), <PERSON><PERSON><PERSON> (20.32), <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (20.56)
BOCHNIA_BBOX = [(49.80, 20.30), (50.15, 20.60)]

# <PERSON><PERSON> p<PERSON><PERSON>towe powiatu bocheńskiego
BOCHNIA_POSTAL_CODES = {
    '32-700', '32-708', '32-709', '32-711', '32-712', '32-720', '32-722',
    '32-724', '32-725', '32-731', '32-732', '32-733', '32-740', '32-741',
    '32-742', '32-744', '32-765', '32-813', '32-015'
}

# Mapowanie kodów pocztowych do powiatów w województwie małopolskim
MALOPOLSKIE_POSTAL_CODES = {
    # Powiat bocheński
    '32-700': 'powiat bocheński', '32-708': 'powiat bocheński', '32-709': 'powiat bocheński',
    '32-711': 'powiat bocheński', '32-712': 'powiat bocheński', '32-720': 'powiat bocheński',
    '32-722': 'powiat bocheński', '32-724': 'powiat bocheński', '32-725': 'powiat bocheński',
    '32-731': 'powiat bocheński', '32-732': 'powiat bocheński', '32-733': 'powiat bocheński',
    '32-740': 'powiat bocheński', '32-741': 'powiat bocheński', '32-742': 'powiat bocheński',
    '32-744': 'powiat bocheński', '32-765': 'powiat bocheński', '32-813': 'powiat bocheński',
    '32-015': 'powiat bocheński',

    # Powiat krakowski
    '32-050': 'powiat krakowski', '32-060': 'powiat krakowski',
    '32-080': 'powiat krakowski', '32-082': 'powiat krakowski', '32-083': 'powiat krakowski',
    '32-085': 'powiat krakowski', '32-090': 'powiat krakowski',

    # Powiat wielicki
    '32-020': 'powiat wielicki', '32-100': 'powiat wielicki', '32-200': 'powiat wielicki',
    '32-300': 'powiat wielicki', '32-332': 'powiat wielicki', '32-340': 'powiat wielicki',

    # Powiat tarnowski
    '33-100': 'powiat tarnowski', '33-110': 'powiat tarnowski', '33-111': 'powiat tarnowski',
    '33-113': 'powiat tarnowski', '33-114': 'powiat tarnowski', '33-120': 'powiat tarnowski',
    '33-122': 'powiat tarnowski', '33-130': 'powiat tarnowski', '33-140': 'powiat tarnowski',
    '33-150': 'powiat tarnowski', '33-160': 'powiat tarnowski', '33-170': 'powiat tarnowski',
    '33-180': 'powiat tarnowski', '33-190': 'powiat tarnowski',

    # Powiat dąbrowski
    '33-200': 'powiat dąbrowski', '33-210': 'powiat dąbrowski', '33-220': 'powiat dąbrowski',
    '33-230': 'powiat dąbrowski', '33-240': 'powiat dąbrowski', '33-250': 'powiat dąbrowski',
    '33-260': 'powiat dąbrowski', '33-270': 'powiat dąbrowski', '33-280': 'powiat dąbrowski',

    # Powiat brzeski
    '32-800': 'powiat brzeski', '32-820': 'powiat brzeski', '32-840': 'powiat brzeski',
    '32-850': 'powiat brzeski', '32-860': 'powiat brzeski', '32-870': 'powiat brzeski',
    '32-874': 'powiat brzeski', '32-876': 'powiat brzeski', '32-880': 'powiat brzeski',

    # Kraków (miasto na prawach powiatu)
    '30-001': 'Kraków', '30-002': 'Kraków', '30-003': 'Kraków', '30-004': 'Kraków',
    '30-005': 'Kraków', '30-006': 'Kraków', '30-007': 'Kraków', '30-008': 'Kraków',
    '30-009': 'Kraków', '30-010': 'Kraków', '31-001': 'Kraków', '31-002': 'Kraków',
    # ... (można dodać więcej kodów Krakowa)
}

# Cache dla adresów - przyspiesza wyszukiwanie powtarzających się adresów
# Klucz: pełny adres, wartość: (lat, lon, powiat_info)
ADDRESS_CACHE = {}

# Cache dla miast - przyspiesza wyszukiwanie powtarzających się miast
CITY_CACHE = {}

def is_valid_result(lat, lon, address_text):
    """
    Sprawdza, czy wynik geokodowania jest prawidłowy.
    Zwraca True jeśli współrzędne są prawidłowe, adres zawiera nazwę województwa
    i kod pocztowy należy do powiatu bocheńskiego.
    TYMCZASOWO WYŁĄCZONE: sprawdzanie bounding box
    """
    if pd.isna(lat) or pd.isna(lon):
        return False

    # TYMCZASOWO WYŁĄCZONE: Sprawdź bounding box
    # if not is_in_bbox(lat, lon, BOCHNIA_BBOX):
    #     return False

    # Jeśli mamy tekst adresu, sprawdź województwo i kod pocztowy
    if isinstance(address_text, str):
        address_lower = address_text.lower()

        # Sprawdź województwo
        has_voivodeship = "małopolskie" in address_lower or "małopolska" in address_lower

        # Sprawdź kod pocztowy
        postal_code = extract_postal_code(address_text)
        has_valid_postal = is_valid_postal_code(postal_code)

        return has_voivodeship and has_valid_postal

    return True  # Jeśli nie ma tekstu adresu, przyjmij że jest OK (dla kompatybilności wstecznej)

def setup_logging():
    """
    Konfiguruje system logowania.
    """
    # Tworzymy nazwę pliku logu z datą i czasem
    log_filename = f"geocoding_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
    
    # Konfiguracja logowania
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_filename, encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )
    return log_filename

def is_in_bbox(lat, lon, bbox):
    """Sprawdza, czy współrzędne znajdują się wewnątrz zadanego obszaru (bounding box)."""
    (min_lat, min_lon), (max_lat, max_lon) = bbox
    return min_lat <= lat <= max_lat and min_lon <= lon <= max_lon

def extract_postal_code(address):
    """
    Wyodrębnia kod pocztowy z adresu.
    Szuka wzorca XX-XXX w dowolnym miejscu adresu.
    """
    import re
    if not isinstance(address, str):
        return None

    # Szukaj wzorca XX-XXX w całym adresie
    match = re.search(r'(\d{2}-\d{3})', address)
    if match:
        return match.group(1)
    return None

def is_valid_postal_code(postal_code):
    """
    Sprawdza, czy kod pocztowy należy do powiatu bocheńskiego.
    """
    return postal_code in BOCHNIA_POSTAL_CODES if postal_code else False

def get_powiat_from_postal_code(postal_code):
    """
    Zwraca nazwę powiatu na podstawie kodu pocztowego.
    """
    if not postal_code:
        return "Nieznany kod pocztowy"

    if postal_code in MALOPOLSKIE_POSTAL_CODES:
        return MALOPOLSKIE_POSTAL_CODES[postal_code]
    else:
        return f"Kod pocztowy {postal_code} spoza województwa małopolskiego"

def extract_powiat_from_address(address_text):
    """
    Wyodrębnia informację o powiecie z adresu zwróconego przez geolokalizator.
    Szuka wzorców takich jak "powiat X" lub "X County".
    """
    import re
    if not isinstance(address_text, str):
        return "Brak informacji"

    address_lower = address_text.lower()

    # Szukaj wzorca "powiat [nazwa]"
    powiat_match = re.search(r'powiat\s+([^,]+)', address_lower)
    if powiat_match:
        return f"powiat {powiat_match.group(1).strip()}"

    # Szukaj wzorca "[nazwa] county"
    county_match = re.search(r'([^,]+)\s+county', address_lower)
    if county_match:
        return f"{county_match.group(1).strip()} county"

    # Sprawdź czy to główne miasta powiatu bocheńskiego
    bochnia_cities = [
        "bochnia", "łapanów", "nowy wiśnicz", "trzciana", "drwinia", "rzezawa",
        "lipnica murowana", "żegocina", "łapczyca", "dąbrowa", "niedomice",
        "wierzchosławice", "raciechowice", "szczurowa", "kłaj", "słomniki",
        "proszowice", "jodłownik", "chronów", "grobla", "leszczyna", "niedary"
    ]

    for city in bochnia_cities:
        if city in address_lower:
            return f"powiat bocheński (rozpoznano miasto: {city})"

    # Jeśli nie znaleziono konkretnego powiatu, sprawdź czy jest województwo
    if "małopolskie" in address_lower or "małopolska" in address_lower:
        return "województwo małopolskie (powiat nieokreślony)"

    return "Powiat nieokreślony"

def is_in_bochnia_powiat(location):
    """
    Sprawdza, czy lokalizacja znajduje się w powiecie bocheńskim.
    PRIORYTET: Jeśli opis zawiera "powiat bocheński", akceptuj bez sprawdzania współrzędnych.
    W przeciwnym razie weryfikuje nazwę województwa i kod pocztowy.
    TYMCZASOWO WYŁĄCZONE: sprawdzanie bounding box
    """
    if not location:
        return False

    address_lower = location.address.lower()
    print(f"🔍 DEBUG: Sprawdzam lokalizację: {location.address}")
    logging.info(f"DEBUG: Sprawdzam lokalizację: {location.address}")

    # PRIORYTET 1: Jeśli opis zawiera "powiat bocheński", akceptuj natychmiast
    if "powiat bocheński" in address_lower or "bocheński county" in address_lower:
        print(f"✅ ZNALEZIONO POWIAT BOCHEŃSKI: {location.address}")
        logging.info(f"✅ Adres zawiera 'powiat bocheński' w opisie - akceptuję: {location.address}")
        return True

    # PRIORYTET 2: Sprawdź czy to inne powiaty w małopolsce - jeśli tak, odrzuć
    other_powiats = [
        "powiat krakowski", "powiat wielicki", "powiat myślenicki", "powiat wadowicki",
        "powiat suski", "powiat nowotarski", "powiat tatrzański", "powiat nowosądecki",
        "powiat gorlicki", "powiat limanowski", "powiat brzeski", "powiat tarnowski",
        "powiat dąbrowski", "powiat proszowicki", "powiat miechowski", "powiat olkuski",
        "powiat chrzanowski", "powiat oświęcimski"
    ]

    for other_powiat in other_powiats:
        if other_powiat in address_lower and other_powiat != "powiat bocheński":
            print(f"❌ ZNALEZIONO INNY POWIAT: {other_powiat} w {location.address}")
            logging.info(f"❌ Adres zawiera inny powiat '{other_powiat}' - odrzucam: {location.address}")
            return False

    # PRIORYTET 3: Sprawdź główne miasta i gminy powiatu bocheńskiego
    bochnia_cities = [
        "bochnia", "łapanów", "nowy wiśnicz", "trzciana", "drwinia", "rzezawa",
        "lipnica murowana", "żegocina", "łapczyca", "dąbrowa", "niedomice",
        "wierzchosławice", "raciechowice", "szczurowa", "kłaj", "słomniki",
        "proszowice", "jodłownik", "chronów", "grobla", "leszczyna", "niedary"
    ]

    for city in bochnia_cities:
        if city in address_lower:
            print(f"✅ ZNALEZIONO MIASTO Z POWIATU BOCHEŃSKIEGO: {city}")
            logging.info(f"✅ Znaleziono miasto z powiatu bocheńskiego: {city}")
            # Sprawdź dodatkowo czy to małopolskie
            if "małopolskie" in address_lower or "małopolska" in address_lower:
                return True

    # Jeśli nie ma "powiat bocheński" w opisie, sprawdź inne kryteria
    # TYMCZASOWO WYŁĄCZONE: Sprawdź bounding box
    # if not is_in_bbox(location.latitude, location.longitude, BOCHNIA_BBOX):
    #     return False

    # Sprawdź, czy w adresie jest "małopolskie" lub "małopolska"
    has_voivodeship = "małopolskie" in address_lower or "małopolska" in address_lower

    # Sprawdź kod pocztowy w adresie
    postal_code = extract_postal_code(location.address)
    has_valid_postal = is_valid_postal_code(postal_code)

    result = has_voivodeship and has_valid_postal
    if result:
        print(f"✅ SPEŁNIA KRYTERIA: województwo={has_voivodeship}, kod_pocztowy={has_valid_postal}")
    else:
        print(f"❌ NIE SPEŁNIA KRYTERIÓW: województwo={has_voivodeship}, kod_pocztowy={has_valid_postal}")

    return result

def geocode_with_retry(geolocator, address, max_retries=2, delay=1, exactly_match=True):
    """
    Próbuje geokodować adres z możliwością ponownych prób w przypadku timeoutu.
    Jeśli exactly_match=True, zwraca tylko wyniki z powiatu bocheńskiego w małopolsce.
    Jeśli geolokalizator zwraca wiele wyników, wybiera ten z powiatu bocheńskiego.
    """
    for attempt in range(max_retries):
        try:
            # Pobierz wszystkie wyniki dla adresu (exactly=False pozwala na wiele wyników)
            locations = geolocator.geocode(address, exactly_one=False, timeout=10)

            if not locations:
                return None

            # Jeśli mamy tylko jeden wynik
            if len(locations) == 1:
                location = locations[0]
                logging.info(f"Znaleziono jeden wynik: {location.address} ({location.latitude}, {location.longitude})")

                if exactly_match:
                    if is_in_bochnia_powiat(location):
                        logging.info(f"✅ Wynik z powiatu bocheńskiego: {location.address}")
                        return location
                    else:
                        logging.warning(f"❌ Wynik poza powiatem bocheńskim: {location.address}. Odrzucanie.")
                        return None
                else:
                    return location

            # Jeśli mamy wiele wyników, szukaj tego z powiatu bocheńskiego
            else:
                logging.info(f"Znaleziono {len(locations)} wyników dla adresu '{address}':")
                for i, loc in enumerate(locations):
                    logging.info(f"  {i+1}. {loc.address} ({loc.latitude}, {loc.longitude})")

                if exactly_match:
                    # Szukaj wyników z powiatu bocheńskiego
                    bochnia_results = []
                    for loc in locations:
                        if is_in_bochnia_powiat(loc):
                            bochnia_results.append(loc)
                            logging.info(f"✅ Znaleziono wynik z powiatu bocheńskiego: {loc.address}")

                    if bochnia_results:
                        # Zwróć pierwszy wynik z powiatu bocheńskiego
                        best_result = bochnia_results[0]
                        logging.info(f"🎯 Wybrano najlepszy wynik: {best_result.address}")
                        return best_result
                    else:
                        logging.warning(f"❌ Żaden z {len(locations)} wyników nie jest z powiatu bocheńskiego. Odrzucanie wszystkich.")
                        return None
                else:
                    # Dla exactly_match=False (kody pocztowe), preferuj wyniki z małopolskiego
                    malopolskie_results = []
                    for loc in locations:
                        address_lower = loc.address.lower()
                        if "małopolskie" in address_lower or "małopolska" in address_lower:
                            malopolskie_results.append(loc)
                            logging.info(f"✅ Znaleziono wynik z małopolskiego: {loc.address}")

                    if malopolskie_results:
                        # Zwróć pierwszy wynik z małopolskiego
                        best_result = malopolskie_results[0]
                        logging.info(f"🎯 Wybrano wynik z małopolskiego: {best_result.address}")
                        return best_result
                    else:
                        # Jeśli nie ma z małopolskiego, zwróć pierwszy wynik
                        logging.info(f"⚠️ Brak wyników z małopolskiego, zwracam pierwszy dostępny: {locations[0].address}")
                        return locations[0]
        except (GeocoderTimedOut, GeocoderServiceError) as e:
            if attempt < max_retries - 1:
                logging.warning(f"Błąd geokodera dla adresu: '{address}'. Próba {attempt + 1}/{max_retries}. Błąd: {e}")
                time.sleep(delay)
                continue
            logging.error(f"Wszystkie próby dla adresu '{address}' nie powiodły się.")
    return None

def parse_csv_address(address):
    """
    Parsuje adres z pliku CSV w różnych formatach:
    - "XX-XXX Miasto,ulica numer"
    - "XX-XXX Miasto,działka nr X"
    - "XX-XXX Miasto,Miasto numer" (dla wiosek)
    Zwraca słownik z komponentami adresu.
    """
    import re
    if not isinstance(address, str):
        return None

    # Wzorzec dla adresu: kod_pocztowy miasto,reszta_adresu
    pattern = r'^(\d{2}-\d{3})\s+([^,]+),(.+)$'
    match = re.match(pattern, address.strip())

    if match:
        postal_code = match.group(1)
        city = match.group(2).strip()
        street_and_number = match.group(3).strip()

        # Sprawdź czy to działka
        is_plot = "działka" in street_and_number.lower()

        # Sprawdź czy nazwa miasta powtarza się w części ulicy (typowe dla wiosek)
        is_village = city.lower() in street_and_number.lower()

        return {
            'postal_code': postal_code,
            'city': city,
            'street_and_number': street_and_number,
            'full_address': address,
            'is_plot': is_plot,
            'is_village': is_village
        }

    return None

def extract_street_without_number(address):
    """
    Próbuje wyodrębnić nazwę ulicy bez numeru z adresu.
    Np. "Kraków,Floriańska 15" -> "Kraków,Floriańska"
    """
    import re
    # Usuń numery z końca (cyfry, litery, znaki specjalne)
    street_pattern = r'^(.+?)[\s,]+\d+[a-zA-Z]*[\s/\-\d]*$'
    match = re.match(street_pattern, address.strip())
    if match:
        return match.group(1).strip()
    return None

def geocode_address(geolocator, address):
    """Geokoduje adres zgodnie z wymaganiami użytkownika"""
    print(f"\n{'='*80}")
    print(f"🔍 SPRAWDZANIE ADRESU: {address}")
    print(f"{'='*80}")

    # Sprawdzamy, czy adres nie jest pusty
    if not isinstance(address, str) or not address.strip():
        print(f"❌ Pusty adres")
        logging.warning("Otrzymano pusty adres. Pomijanie.")
        return None, None, "Brak adresu"

    # Sprawdź cache
    if address in ADDRESS_CACHE:
        cached_result = ADDRESS_CACHE[address]
        print(f"💾 CACHE HIT: Używam zapisanego wyniku")
        print(f"   📍 Współrzędne: {cached_result[0]}, {cached_result[1]}")
        print(f"   🏛️ Powiat: {cached_result[2]}")
        # Oznacz wynik jako pochodzący z cache
        cache_result = (cached_result[0], cached_result[1], f"Cache: {cached_result[2]}")
        return cache_result

    # Parsuj adres z pliku CSV
    parsed_address = parse_csv_address(address)
    if not parsed_address:
        print(f"❌ Nie udało się sparsować adresu")
        logging.warning(f"Nie udało się sparsować adresu: {address}")
        return None, None, "Błąd parsowania"

    postal_code = parsed_address['postal_code']
    city = parsed_address['city']
    street_and_number = parsed_address['street_and_number']
    is_plot = parsed_address['is_plot']
    is_village = parsed_address['is_village']

    print(f"📋 PARSOWANIE: kod={postal_code}, miasto={city}, ulica={street_and_number}")
    if is_plot:
        print(f"   📍 Wykryto działkę")
    if is_village:
        print(f"   🏘️ Wykryto wioskę (nazwa miasta w adresie)")

    # Sprawdź kod pocztowy przed rozpoczęciem geokodowania
    if not is_valid_postal_code(postal_code):
        print(f"❌ Kod pocztowy {postal_code} nie należy do powiatu bocheńskiego")
        logging.warning(f"Kod pocztowy {postal_code} nie należy do powiatu bocheńskiego. Pomijanie adresu: {address}")
        return None, None, f"Nieprawidłowy kod pocztowy: {postal_code}"

    # STRATEGIA 1: Pełny adres (kod pocztowy + miejscowość + ulica + numer)
    # Dla działek i wiosek używamy różnych formatów
    if is_plot:
        # Dla działek szukamy tylko miejscowości z kodem pocztowym
        full_address = f"{postal_code} {city}"
        print(f"\n🎯 STRATEGIA 1: Działka - kod pocztowy + miejscowość")
        print(f"   Szukam: '{full_address}' (działka - pomijam szczegóły)")
    elif is_village:
        # Dla wiosek używamy pełnego adresu
        full_address = f"{postal_code} {city}, {street_and_number}"
        print(f"\n🎯 STRATEGIA 1: Wioska - pełny adres")
        print(f"   Szukam: '{full_address}'")
    else:
        # Standardowy adres miejski
        full_address = f"{postal_code} {city}, {street_and_number}"
        print(f"\n🎯 STRATEGIA 1: Pełny adres")
        print(f"   Szukam: '{full_address}'")

    location = geocode_with_retry(geolocator, full_address, exactly_match=True)
    if location:
        print(f"   ✅ ZNALEZIONO: {location.address}")
        powiat_info = extract_powiat_from_address(location.address)
        print(f"   🎉 SUKCES! Strategia 1 - {powiat_info}")
        result = (location.latitude, location.longitude, f"Strategia 1: {powiat_info}")
        ADDRESS_CACHE[address] = result  # Zapisz w cache
        return result
    else:
        print(f"   ❌ Nie znaleziono w powiecie bocheńskim")

    # STRATEGIA 2: Bez numeru budynku (kod pocztowy + miejscowość + ulica)
    # Pomijamy dla działek, bo już próbowaliśmy miejscowość
    if not is_plot:
        street_only = street_and_number.split()[0] if street_and_number else ""
        if street_only and not is_village:  # Dla wiosek nazwa ulicy to często nazwa miasta
            address_no_number = f"{postal_code} {city}, {street_only}"
            print(f"\n🎯 STRATEGIA 2: Bez numeru budynku")
            print(f"   Szukam: '{address_no_number}'")

            location = geocode_with_retry(geolocator, address_no_number, exactly_match=True)
            if location:
                print(f"   ✅ ZNALEZIONO: {location.address}")
                powiat_info = extract_powiat_from_address(location.address)
                print(f"   🎉 SUKCES! Strategia 2 - {powiat_info}")
                result = (location.latitude, location.longitude, f"Strategia 2: {powiat_info}")
                ADDRESS_CACHE[address] = result  # Zapisz w cache
                return result
            else:
                print(f"   ❌ Nie znaleziono w powiecie bocheńskim")
        else:
            print(f"\n⏭️ STRATEGIA 2: Pomijam (działka lub wioska)")
    else:
        print(f"\n⏭️ STRATEGIA 2: Pomijam (działka - już sprawdzono miejscowość)")

    # STRATEGIA 3: Tylko kod pocztowy + miejscowość
    city_only = f"{postal_code} {city}"
    print(f"\n🎯 STRATEGIA 3: Kod pocztowy + miejscowość")
    print(f"   Szukam: '{city_only}'")

    location = geocode_with_retry(geolocator, city_only, exactly_match=True)
    if location:
        print(f"   ✅ ZNALEZIONO: {location.address}")
        powiat_info = extract_powiat_from_address(location.address)
        print(f"   🎉 SUKCES! Strategia 3 - {powiat_info}")
        result = (location.latitude, location.longitude, f"Strategia 3: {powiat_info}")
        ADDRESS_CACHE[address] = result  # Zapisz w cache
        return result
    else:
        print(f"   ❌ Nie znaleziono w powiecie bocheńskim")

    # STRATEGIA 4: Tylko kod pocztowy
    print(f"\n🎯 STRATEGIA 4: Tylko kod pocztowy")
    print(f"   Szukam: '{postal_code}'")

    # Sprawdź jaki powiat jest przypisany do tego kodu pocztowego
    expected_powiat = get_powiat_from_postal_code(postal_code)
    print(f"   📍 Kod pocztowy {postal_code} powinien należeć do: {expected_powiat}")

    location = geocode_with_retry(geolocator, postal_code, exactly_match=False)  # Nie wymagamy dokładnego dopasowania dla kodu pocztowego
    if location:
        print(f"   ✅ ZNALEZIONO DLA KODU POCZTOWEGO: {location.address}")
        powiat_info = extract_powiat_from_address(location.address)

        # Sprawdź czy kod pocztowy prowadzi do małopolskiego
        address_lower = location.address.lower()
        if "małopolskie" in address_lower or "małopolska" in address_lower:
            print(f"   🎉 SUKCES! Strategia 4 - kod pocztowy w województwie małopolskim")
            # Dodaj informację o oczekiwanym powiecie
            powiat_info_with_note = f"Strategia 4: {powiat_info} (tylko kod pocztowy, oczekiwany: {expected_powiat})"
            result = (location.latitude, location.longitude, powiat_info_with_note)
            ADDRESS_CACHE[address] = result  # Zapisz w cache
            return result
        else:
            print(f"   ❌ Kod pocztowy poza województwem małopolskim")
            # Nawet jeśli poza małopolskim, zwróć wynik z informacją o oczekiwanym powiecie
            powiat_info_with_note = f"Strategia 4: {powiat_info} (tylko kod pocztowy, oczekiwany: {expected_powiat})"
            result = (location.latitude, location.longitude, powiat_info_with_note)
            ADDRESS_CACHE[address] = result  # Zapisz w cache
            return result
    else:
        print(f"   ❌ Nie znaleziono dla kodu pocztowego")

    # STRATEGIA 5: Ostatnia szansa - tylko nazwa miasta
    print(f"\n🎯 STRATEGIA 5: Tylko nazwa miasta (ostatnia szansa)")
    print(f"   Szukam: '{city}'")
    print(f"   📍 Miasto {city} powinno należeć do: powiat bocheński (na podstawie kodu pocztowego {postal_code})")

    location = geocode_with_retry(geolocator, city, exactly_match=False)
    if location:
        print(f"   ✅ ZNALEZIONO DLA MIASTA: {location.address}")
        powiat_info = extract_powiat_from_address(location.address)

        # Sprawdź czy miasto prowadzi do małopolskiego
        address_lower = location.address.lower()
        if "małopolskie" in address_lower or "małopolska" in address_lower:
            print(f"   🎉 SUKCES! Strategia 5 - miasto w województwie małopolskim")
            powiat_info_with_note = f"Strategia 5: {powiat_info} (tylko nazwa miasta, oczekiwany: {expected_powiat})"
            result = (location.latitude, location.longitude, powiat_info_with_note)
            ADDRESS_CACHE[address] = result  # Zapisz w cache
            return result
        else:
            print(f"   ❌ Miasto poza województwem małopolskim")
            # Nawet jeśli poza małopolskim, zwróć wynik z informacją o oczekiwanym powiecie
            powiat_info_with_note = f"Strategia 5: {powiat_info} (tylko nazwa miasta, oczekiwany: {expected_powiat})"
            result = (location.latitude, location.longitude, powiat_info_with_note)
            ADDRESS_CACHE[address] = result  # Zapisz w cache
            return result
    else:
        print(f"   ❌ Nie znaleziono nawet dla nazwy miasta")

    print(f"\n❌ BRAK WYNIKÓW dla adresu: {address}")
    logging.warning(f"Nie znaleziono współrzędnych dla żadnej strategii: {address}")
    result = (None, None, "Strategia: Nie znaleziono")
    ADDRESS_CACHE[address] = result  # Zapisz w cache również negatywne wyniki
    return result

def load_existing_results(output_file):
    """
    Wczytuje istniejące wyniki z pliku wyjściowego jeśli istnieje.
    """
    if os.path.exists(output_file):
        try:
            existing_df = pd.read_csv(output_file, sep=',', quotechar='"')
            logging.info(f"Znaleziono istniejący plik wyników: {output_file}")
            logging.info(f"Wczytano {len(existing_df)} już przetworzonych wierszy")
            return existing_df
        except Exception as e:
            logging.warning(f"Nie udało się wczytać istniejącego pliku: {e}")
    return None

def save_progress(df, output_file):
    """
    Zapisuje aktualny postęp do pliku.
    """
    try:
        df.to_csv(output_file, sep=',', index=False, quoting=1)  # quoting=1 oznacza QUOTE_ALL
        logging.debug(f"Zapisano postęp do pliku: {output_file}")
    except Exception as e:
        logging.error(f"Błąd podczas zapisywania postępu: {e}")

def process_csv(input_file):
    """
    Przetwarza plik CSV z adresami i zapisuje wyniki geokodowania.
    Obsługuje wznowienie pracy po przerwaniu i zapisuje wyniki na bieżąco.
    """
    try:
        logging.info(f"Rozpoczynam przetwarzanie pliku: {input_file}")

        # Wczytaj dane - używamy przecinka jako separatora i obsługujemy cudzysłowy
        df = pd.read_csv(input_file, sep=',', quotechar='"', skipinitialspace=True)
        logging.info(f"Wczytano {len(df)} wierszy z pliku")
        logging.info(f"Kolumny w pliku: {list(df.columns)}")

        # Przygotuj plik wyjściowy
        output_file = input_file.rsplit('.', 1)[0] + '_geocoded.csv'

        # Sprawdź, czy istnieją już wyniki
        existing_df = load_existing_results(output_file)
        if existing_df is not None and 'latitude' in existing_df.columns and 'longitude' in existing_df.columns:
            # Określ kolumny do scalenia
            merge_columns = ['id', 'latitude', 'longitude']
            if 'powiat_geolokalizator' in existing_df.columns:
                merge_columns.append('powiat_geolokalizator')

            # Połącz z istniejącymi wynikami
            df = df.merge(existing_df[merge_columns], on='id', how='left', suffixes=('', '_existing'))
            # Użyj istniejących współrzędnych jeśli są dostępne
            if 'latitude_existing' in df.columns:
                df['latitude'] = df['latitude_existing'].fillna(df.get('latitude'))
                df['longitude'] = df['longitude_existing'].fillna(df.get('longitude'))
                if 'powiat_geolokalizator_existing' in df.columns:
                    df['powiat_geolokalizator'] = df['powiat_geolokalizator_existing'].fillna(df.get('powiat_geolokalizator'))
                df = df.drop(columns=['latitude_existing', 'longitude_existing', 'powiat_geolokalizator_existing'], errors='ignore')

        # Dodaj kolumny dla współrzędnych i powiatu jeśli nie istnieją
        if 'latitude' not in df.columns:
            df['latitude'] = None
        if 'longitude' not in df.columns:
            df['longitude'] = None
        if 'powiat_geolokalizator' not in df.columns:
            df['powiat_geolokalizator'] = None

        # Inicjalizuj geokoder
        logging.info("Inicjalizacja geokodera...")
        ssl_context = ssl.create_default_context(cafile=certifi.where())
        geolocator = Nominatim(user_agent="my_geocoder", ssl_context=ssl_context)
        logging.info("Geokoder gotowy.")

        # Przetwarzaj każdy wiersz
        successful_count = 0
        failed_count = 0
        processed_count = 0
        cache_hits = 0
        strategy_stats = {
            'Strategia 1': 0,
            'Strategia 2': 0,
            'Strategia 3': 0,
            'Strategia 4': 0,
            'Strategia 5': 0,
            'Cache': 0,
            'Nie znaleziono': 0
        }

        for i in tqdm(range(len(df)), desc="Geokodowanie adresów",
                     bar_format='{l_bar}{bar}| {n_fmt}/{total_fmt} [{elapsed}<{remaining}, {rate_fmt}] Sukces: {postfix[0]} Cache: {postfix[1]}',
                     postfix=[0, 0]):
            current_pos = i + 1
            address = df.iloc[i]['adress']
            row_id = df.iloc[i]['id']
            row_lat = df.iloc[i].get('latitude', None)
            row_lon = df.iloc[i].get('longitude', None)

            logging.info(f"[{current_pos}/{len(df)}] Przetwarzanie adresu ID {row_id}: {address}")

            # Sprawdź, czy adres już został przetworzony i czy wynik jest prawidłowy
            if pd.notna(row_lat) and pd.notna(row_lon):
                if is_valid_result(row_lat, row_lon, address):
                    logging.info(f"Adres już przetworzony z prawidłowymi współrzędnymi. Pomijanie.")
                    successful_count += 1
                    continue
                else:
                    logging.info(f"Adres przetworzony, ale współrzędne poza powiatem bocheńskim lub nieprawidłowy kod pocztowy. Ponowne przetwarzanie.")

            # Geokoduj adres
            try:
                lat, lon, powiat_info = geocode_address(geolocator, address)
                df.at[i, 'latitude'] = lat
                df.at[i, 'longitude'] = lon
                df.at[i, 'powiat_geolokalizator'] = powiat_info

                # Aktualizuj statystyki
                if powiat_info and "Cache" in str(powiat_info):
                    cache_hits += 1
                    strategy_stats['Cache'] += 1
                elif powiat_info:
                    # Wyodrębnij strategię z powiat_info
                    for strategy in strategy_stats.keys():
                        if strategy in str(powiat_info):
                            strategy_stats[strategy] += 1
                            break
                    else:
                        if "Nie znaleziono" in str(powiat_info):
                            strategy_stats['Nie znaleziono'] += 1

                if lat is not None and lon is not None:
                    successful_count += 1
                    logging.info(f"Pomyślnie przetworzono adres: {address} - powiat: {powiat_info}")
                else:
                    failed_count += 1
                    logging.warning(f"Nie udało się przetworzyć adresu: {address}")
            except Exception as e:
                failed_count += 1
                strategy_stats['Nie znaleziono'] += 1
                logging.error(f"Błąd podczas przetwarzania adresu '{address}': {e}")
                df.at[i, 'latitude'] = None
                df.at[i, 'longitude'] = None
                df.at[i, 'powiat_geolokalizator'] = "Błąd przetwarzania"

            processed_count += 1

            # Aktualizuj pasek postępu
            tqdm.write(f"Postęp: {successful_count}/{processed_count} sukces, Cache: {cache_hits}")

            # Zapisuj postęp co 10 adresów
            if processed_count % 10 == 0:
                save_progress(df, output_file)

            # Dodaj małe opóźnienie, aby nie przekroczyć limitów API
            time.sleep(0.5)

        # Zapisz końcowe wyniki
        save_progress(df, output_file)
        logging.info(f"Zapisano wyniki do pliku: {output_file}")

        # Podsumowanie
        print(f"\n{'='*80}")
        print(f"📊 PODSUMOWANIE GEOKODOWANIA")
        print(f"{'='*80}")
        print(f"✅ Pomyślnie przetworzono: {successful_count} adresów")
        print(f"❌ Nie udało się przetworzyć: {failed_count} adresów")
        print(f"💾 Trafienia cache: {cache_hits} adresów")
        print(f"\n📈 STATYSTYKI STRATEGII:")
        for strategy, count in strategy_stats.items():
            if count > 0:
                percentage = (count / processed_count) * 100
                print(f"   {strategy}: {count} adresów ({percentage:.1f}%)")

        logging.info("Podsumowanie:")
        logging.info(f"- Pomyślnie przetworzono: {successful_count} adresów")
        logging.info(f"- Nie udało się przetworzyć: {failed_count} adresów")
        logging.info(f"- Trafienia cache: {cache_hits} adresów")
        logging.info("Statystyki strategii:")
        for strategy, count in strategy_stats.items():
            if count > 0:
                percentage = (count / processed_count) * 100
                logging.info(f"  {strategy}: {count} adresów ({percentage:.1f}%)")

    except Exception as e:
        logging.error(f"Wystąpił błąd podczas przetwarzania pliku: {str(e)}")

if __name__ == "__main__":
    # Konfiguracja logowania
    log_filename = setup_logging()
    logging.info("Rozpoczęcie programu geokodowania adresów")
    
    input_file = "powiat bocheński - adresy_geocode.csv"
    process_csv(input_file)
    
    logging.info("Zakończenie programu geokodowania adresów") 